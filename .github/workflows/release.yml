name: Release

on:
  push:
    tags:
      - "v*"

permissions:
  contents: write

jobs:
  build:
    name: Build ${{ matrix.target }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            archive: tar.gz
            exe: ""
          - os: macos-14
            target: aarch64-apple-darwin
            archive: tar.gz
            exe: ""
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            archive: zip
            exe: ".exe"

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Build
        run: cargo build --release --target ${{ matrix.target }}

      - name: Prepare package (unix)
        if: matrix.archive == 'tar.gz'
        shell: bash
        run: |
          set -euxo pipefail
          BIN=av
          OUT_DIR=dist
          mkdir -p "$OUT_DIR"
          SRC="target/${{ matrix.target }}/release/${BIN}"
          ASSET="${BIN}-${GITHUB_REF_NAME}-${{ matrix.target }}.tar.gz"
          tar -C "target/${{ matrix.target }}/release" -czf "$OUT_DIR/$ASSET" "$BIN"

      - name: Prepare package (windows)
        if: matrix.archive == 'zip'
        shell: pwsh
        run: |
          $ErrorActionPreference = 'Stop'
          $Bin = 'av.exe'
          $OutDir = 'dist'
          New-Item -ItemType Directory -Force -Path $OutDir | Out-Null
          $Src = "target/${{ matrix.target }}/release/$Bin"
          $Asset = "av-$env:GITHUB_REF_NAME-${{ matrix.target }}.zip"
          Compress-Archive -Path $Src -DestinationPath "$OutDir/$Asset"

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: av-${{ github.ref_name }}-${{ matrix.target }}
          path: dist/*
          if-no-files-found: error

  release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: build
    permissions:
      contents: write
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: dist

      - name: Publish Release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            dist/**
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.PRIVATE_TOKEN }}
