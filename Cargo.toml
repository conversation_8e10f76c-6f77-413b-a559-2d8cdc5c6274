[package]
name = "av"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
clap = { version = "4.5", features = ["derive"] }
colored = "2.1"
indicatif = "0.17"
regex = "1.10"
reqwest = { version = "0.12", features = ["gzip", "brotli", "deflate", "json", "cookies", "rustls-tls"] }
scraper = "0.19"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.38", features = ["rt-multi-thread", "macros", "process"] }
urlencoding = "2.1"
which = "6.0"
tempfile = "3.10"
